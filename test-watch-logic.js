// 测试 watch 逻辑
const dayjs = require('dayjs');

// 测试 watch 逻辑
const testWatchLogic = (activityRange, signUpRange) => {
  console.log('\n=== 测试 watch 逻辑 ===');
  console.log('活动时间:', activityRange);
  console.log('报名时间:', signUpRange);
  
  if (signUpRange[0] && signUpRange[1]) {
    if (!activityRange[0] || !activityRange[1]) {
      console.log('结果: 活动时间被清空，应该清空报名时间');
      return true; // 应该清空
    } else {
      const signUpStart = dayjs(signUpRange[0]);
      const signUpEnd = dayjs(signUpRange[1]);
      const activityStart = dayjs(activityRange[0]);
      const activityEnd = dayjs(activityRange[1]);
      
      const isSignUpStartTooEarly = signUpStart.isBefore(activityStart);
      const isSignUpEndTooLate = signUpEnd.isAfter(activityEnd);
      
      console.log('详细检查:', {
        signUpStart: signUpStart.format('YYYY-MM-DD HH:mm:ss'),
        signUpEnd: signUpEnd.format('YYYY-MM-DD HH:mm:ss'),
        activityStart: activityStart.format('YYYY-MM-DD HH:mm:ss'),
        activityEnd: activityEnd.format('YYYY-MM-DD HH:mm:ss'),
        isSignUpStartTooEarly,
        isSignUpEndTooLate,
        shouldClear: isSignUpStartTooEarly || isSignUpEndTooLate
      });
      
      if (isSignUpStartTooEarly || isSignUpEndTooLate) {
        console.log('结果: 报名时间超出范围，应该清空');
        return true; // 应该清空
      } else {
        console.log('结果: 报名时间在有效范围内，保持不变');
        return false; // 不应该清空
      }
    }
  } else {
    console.log('结果: 报名时间为空，无需检查');
    return false; // 不需要清空
  }
};

// 你的具体场景
console.log('你的场景：活动时间 2025-07-11 11:59:36-2025-07-11 11:59:36，报名时间 2025-07-11 11:59:25-2025-07-31 11:59:27');
const shouldClear = testWatchLogic(
  ['2025-07-11 11:59:36', '2025-07-11 11:59:36'],
  ['2025-07-11 11:59:25', '2025-07-31 11:59:27']
);

console.log('\n最终结论:', shouldClear ? '会被清空（这是bug）' : '不会被清空（正确）');
