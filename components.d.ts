/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ContentModal: typeof import('./src/components/contentModal.vue')['default']
    EditFy: typeof import('./src/components/oldProject/editor/EditFy.vue')['default']
    EditHshd: typeof import('./src/components/oldProject/editor/EditHshd.vue')['default']
    EditHyra: typeof import('./src/components/oldProject/editor/EditHyra.vue')['default']
    EditJf: typeof import('./src/components/oldProject/editor/EditJf.vue')['default']
    EditKV: typeof import('./src/components/oldProject/editor/EditKV.vue')['default']
    EditRtnbtn: typeof import('./src/components/oldProject/editor/EditRtnbtn.vue')['default']
    EditSmartCard: typeof import('./src/components/oldProject/editor/EditSmartCard.vue')['default']
    EditYbdwjs: typeof import('./src/components/oldProject/editor/EditYbdwjs.vue')['default']
    EditYhjc: typeof import('./src/components/oldProject/editor/EditYhjc.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    LineChart: typeof import('./src/components/lineChart.vue')['default']
    LzBaseHeader: typeof import('./src/components/oldProject/layouts/LzBaseHeader.vue')['default']
    LzBaseSide: typeof import('./src/components/oldProject/layouts/LzBaseSide.vue')['default']
    LzBreadcrumb: typeof import('./src/components/oldProject/layouts/LzBreadcrumb.vue')['default']
    LzButtonGroup: typeof import('./src/components/oldProject/layouts/LzButtonGroup.vue')['default']
    LzConditionPanel: typeof import('./src/components/oldProject/layouts/LzConditionPanel.vue')['default']
    LzFormEditPage: typeof import('./src/components/oldProject/layouts/LzFormEditPage.vue')['default']
    LzFormPage: typeof import('./src/components/oldProject/layouts/LzFormPage.vue')['default']
    LzImageUploader: typeof import('./src/components/oldProject/uploader/LzImageUploader.vue')['default']
    LzPage: typeof import('./src/components/oldProject/layouts/LzPage.vue')['default']
    LzPagination: typeof import('./src/components/pagination/LzPagination.vue')['default']
    LzPdfUploader: typeof import('./src/components/oldProject/uploader/LzPdfUploader.vue')['default']
    LzPhone: typeof import('./src/components/lzPhone/LzPhone.vue')['default']
    LzTablePanel: typeof import('./src/components/oldProject/layouts/LzTablePanel.vue')['default']
    LzToolPanel: typeof import('./src/components/oldProject/layouts/LzToolPanel.vue')['default']
    LzWangEditor: typeof import('./src/components/oldProject/editor/LzWangEditor.vue')['default']
    MyUpload: typeof import('./src/components/MyUpload.vue')['default']
    PieCharts: typeof import('./src/components/PieCharts.vue')['default']
    ProductPicker: typeof import('./src/components/productPicker/index.vue')['default']
    ProductSearch: typeof import('./src/components/productPicker/ProductSearch/ProductSearch.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectedProductsDialog: typeof import('./src/components/productPicker/SelectedProductsDialog/SelectedProductsDialog.vue')['default']
    SystemButton: typeof import('./src/components/SystemButton.vue')['default']
  }
}
