<template>
  <div class="product-picker-wrapper">
    <el-space :size="16" alignment="center">
      <el-button 
        type="primary" 
        @click="handleClick"
        :disabled="disabled"
      >
        添加商品 ({{ currentSelected.length }}/{{ max }})
      </el-button>
      <el-button 
        type="primary" 
        link 
        @click="handleViewSelected"
      >
        查看已选商品
      </el-button>
    </el-space>

    <!-- 商品选择弹窗 -->
    <el-dialog
      v-model="showProductDialog"
      title="选择商品"
      width="850px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <ProductSearch
        :min="min"
        :max="max"
        :selected-items="currentSelected"
        :disabled-items="disabledItems"
        :item-identifier="itemIdentifier"
        :show-sale-status-filter="showSaleStatusFilter"
        @success="handleProductSearchSuccess"
        @close-dialog="handleProductSearchClose"
      />
    </el-dialog>

    <!-- 已选商品管理弹窗 -->
    <SelectedProductsDialog
      v-if="showSelectedModal"
      :items="currentSelected"
      :item-identifier="itemIdentifier"
      :disabled="disabled"
      @close="() => showSelectedModal = false"
      @save="handleSelectedModalSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElSpace, ElButton, ElDialog } from 'element-plus'
import { useRoute } from 'vue-router'
import CommonApi from '/@/utils/request/api/commonApi'
import ProductSearch from './ProductSearch/ProductSearch.vue'
import SelectedProductsDialog from './SelectedProductsDialog/SelectedProductsDialog.vue'

interface OrderSku {
  productId: string
  skuName: string
  skuMainPicture: string
  sellNum?: number
  [key: string]: any
}

interface ProductPickerProps {
  min?: number
  max?: number
  selectedItems?: OrderSku[]
  disabledItems?: OrderSku[]
  itemIdentifier?: string
  showSaleStatusFilter?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<ProductPickerProps>(), {
  min: 1,
  max: 99,
  selectedItems: () => [],
  disabledItems: () => [],
  itemIdentifier: 'productId',
  showSaleStatusFilter: true,
  disabled: false
})

const emit = defineEmits<{
  selectedProducts: [selected: OrderSku[]]
}>()

const route = useRoute()

// 从URL参数中获取shopId
const getShopId = () => {
  return route.query.shopId as string || ''
}

const currentSelected = ref<OrderSku[]>([])
const showSelectedModal = ref(false)
const showProductDialog = ref(false)

// API function using CommonApi
const getProductList = async (params: any): Promise<{ data: OrderSku[], total: number }> => {
  try {
    const requestData = {
      ...params,
      shopId: getShopId()
    }
    const response: any = await CommonApi.getProductList(requestData)
    return {
      data: response?.data || [],
      total: response?.total || 0
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    throw error
  }
}

// 当 selectedItems 改变时，如果只有 productId，则调用接口补全信息
watch(() => props.selectedItems, async (newItems) => {
  if (newItems.length === 0) {
    return
  }
  if (newItems.length > 0 && 
      newItems.every((item: any) => Object.keys(item).length === 1 && item.productId)) {
    try {
      const q = newItems.map((item: any) => item.productId).join(',')
      const len = newItems.length
      const res = await getProductList({ productIds: q, pageNum: 1, pageSize: len })
      if (Array.isArray(res.data)) {
        currentSelected.value = res.data
      } else {
        currentSelected.value = []
      }
    } catch (err) {
      currentSelected.value = []
    }
  } else {
    // 已经有完整信息，直接使用
    currentSelected.value = [...newItems]
  }
}, { immediate: true })

// 打开"选择商品"弹窗
const handleClick = () => {
  showProductDialog.value = true
}

// 商品选择成功回调
const handleProductSearchSuccess = (selectedProducts: OrderSku[]) => {
  currentSelected.value = selectedProducts
  emit('selectedProducts', selectedProducts)
  showProductDialog.value = false
}

// 商品选择弹窗关闭回调
const handleProductSearchClose = () => {
  showProductDialog.value = false
}

// 打开"已选商品"管理弹窗
const handleViewSelected = () => {
  showSelectedModal.value = true
}

// "已选商品"弹窗保存
const handleSelectedModalSave = (updatedItems: OrderSku[]) => {
  currentSelected.value = updatedItems
  emit('selectedProducts', updatedItems)
}
</script>

<style scoped>
.product-picker-wrapper {
  margin-top: 16px;
}
</style>