<template>
  <div class="product-content">
    <!-- Tab 切换 -->
    <el-tabs v-model="activeTab" class="product-tabs">
      <el-tab-pane label="搜索商品" name="search">
        <!-- 查询条件 -->
        <el-row justify="space-between" style="margin-bottom: 16px;">
          <el-space :size="10">
              <el-select
                v-if="showSaleStatusFilter"
                v-model="searchParams.status"
                @change="handleApproveStatusChange"
                style="width: 120px;"
              >
                <el-option label="全部" :value="-1" />
                <el-option label="已上架" :value="0" />
                <el-option label="已下架" :value="1" />
              </el-select>
            </el-space>
            <el-space :size="10">
              <el-input
                v-model="inputValue"
                :placeholder="inputSelect === 'id' ? '请输入商品ID，多个商品用\',\'分隔' : '请输入商品名称'"
                style="width: 350px;"
                clearable
                @input="handleInputChange"
              >
                <template #prepend>
                  <el-select 
                    v-model="inputSelect" 
                    @change="handleInputSelectChange"
                    style="width: 120px;"
                  >
                    <el-option label="商品ID" value="id" />
                    <el-option label="商品名称" value="name" />
                  </el-select>
                </template>
              </el-input>
              <el-button @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-space>
        </el-row>

        <!-- 全选按钮 -->
        <el-row v-if="max > 1" style="margin-bottom: 16px;">
          <el-checkbox
            v-model="selectAllChecked"
            @change="handleSelectAll"
          >
            全选
          </el-checkbox>
        </el-row>

        <!-- 商品列表 -->
        <div v-loading="loading" class="product-grid-list">
          <div
            v-for="product in products"
            :key="product.productId"
            :class="[
              'product-item',
              { 'disabled': isProductDisabled(product) },
              { 'selected': isProductSelected(product) }
            ]"
          >
            <div class="product-img">
              <img
                :src="product.skuMainPicture"
                :alt="product.skuName"
                @click="() => handleProductSelect(product)"
              />
              <el-checkbox
                :model-value="isProductSelected(product)"
                :disabled="isProductDisabled(product)"
                class="checkbox"
                @change="() => handleProductSelect(product)"
              />
            </div>
            <span class="product-title">{{ product.skuName }}</span>
            <div class="product-info">
              <span class="product-price"></span>
              <div class="product-stock">
                销量
                <span class="product-stock-num">{{ product.sellNum || 0 }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页和操作按钮 -->
        <el-row 
          justify="end" 
          style="margin-top: 24px; margin-bottom: 16px;"
        >
        <el-pagination
              v-model:current-page="paginationProps.current"
              v-model:page-size="paginationProps.pageSize"
              :total="paginationProps.total"
              :page-sizes="[30, 60]"
              layout="total, sizes, prev, pager, next"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
            <el-space :size="16">
              <el-space :size="10">
                <span class="product-selected-num">已选择：{{ selectedProducts.length }} / </span>
                <span>{{ paginationProps.total }}</span>
              </el-space>
              <el-button @click="handleCancel">
                取消
              </el-button>
              <el-button
                type="primary"
                @click="handleConfirm"
              >
                确定
              </el-button>
            </el-space>
        </el-row>
      </el-tab-pane>
      
      <el-tab-pane label="导入商品" name="import">
        <div class="import-panel">
          <el-form :model="importForm" label-width="120px">
            <el-form-item label="商品ID列表">
              <el-input
                v-model="importForm.skuIds"
                type="textarea"
                :rows="8"
                placeholder="请输入商品ID，多个ID请用逗号分隔，例如：1001,1002,1003"
              />
            </el-form-item>
            <el-form-item>
              <el-space>
                <el-button type="primary" @click="handleImportProducts" :loading="importLoading">
                  导入商品
                </el-button>
                <el-button @click="clearImportForm">清空</el-button>
              </el-space>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElRow, ElSelect, ElOption, ElInput, ElButton, ElCheckbox, ElPagination, ElSpace, ElTabs, ElTabPane, ElForm, ElFormItem, ElDivider } from 'element-plus'
import { uniqBy } from 'lodash'
import { useRoute } from 'vue-router'
import CommonApi from '/@/utils/request/api/commonApi'

interface OrderSku {
  productId: string
  skuName: string
  skuMainPicture: string
  sellNum?: number
  status?: number
  [key: string]: any
}

interface ProductSearchProps {
  min?: number
  max?: number
  selectedItems?: OrderSku[]
  disabledItems?: OrderSku[]
  itemIdentifier?: string
  showSaleStatusFilter?: boolean
}

const props = withDefaults(defineProps<ProductSearchProps>(), {
  min: 1,
  max: 99,
  selectedItems: () => [],
  disabledItems: () => [],
  itemIdentifier: 'productId',
  showSaleStatusFilter: true
})

const emit = defineEmits<{
  success: [products: OrderSku[]]
  closeDialog: []
}>()

const route = useRoute()

// 从URL参数中获取shopId
const getShopId = () => {
  return route.query.shopId as string || ''
}

// API function using CommonApi
const getProductList = async (params: any): Promise<{ data: OrderSku[], total: number }> => {
  try {
    const requestData = {
      ...params,
      shopId: getShopId()
    }
    const response: any = await CommonApi.getProductList(requestData)
    return {
      data: response.data || [],
      total: response.total || 0
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    throw error
  }
}

const handleCancel = () => {
  emit('closeDialog')
}

// API function for importing products by IDs
const getProductsByIds = async (productIds: string[]): Promise<OrderSku[]> => {
  try {
    const requestData = {
      productIds: productIds.join(','),
      shopId: getShopId(),
      pageNum: 1,
      pageSize: 10000
    }
    const response: any = await CommonApi.getProductList(requestData)
    return response.data || []
  } catch (error) {
    console.error('根据ID获取商品失败:', error)
    throw error
  }
}

// 初始化搜索参数
const initData = {
  productIds: '',
  name: '',
  status: -1,
}

const searchParams = reactive({ ...initData })
const products = ref<OrderSku[]>([])
const selectedProducts = ref<OrderSku[]>([])
const selectAllChecked = ref(false)
const loading = ref(false)
const inputSelect = ref('id')
const inputValue = ref('')
const activeTab = ref('search')
const importLoading = ref(false)
const importResults = ref<OrderSku[]>([])
const importForm = reactive({
  skuIds: ''
})

// 分页参数
const paginationProps = reactive({
  current: 1,
  pageSize: 30,
  total: 0
})

onMounted(async () => {
  const productList = await fetchData({
    ...searchParams,
    pageNum: paginationProps.current,
    pageSize: paginationProps.pageSize,
  })
  
  // 初始化时勾选已选中的商品
  if (props.selectedItems && props.itemIdentifier) {
    let initialSelectedProducts = props.selectedItems.map(p => ({ ...p }))
    initialSelectedProducts = uniqBy(initialSelectedProducts, props.itemIdentifier)
    selectedProducts.value = initialSelectedProducts
    selectAllChecked.value = isAllProductsSelected(productList, initialSelectedProducts)
  } else {
    selectAllChecked.value = isAllProductsSelected(productList)
  }
})

// 判断当前页面是否所有可选商品都已被选中
const isAllProductsSelected = (productList?: OrderSku[], selectList?: OrderSku[]) => {
  const currentProducts = productList || products.value
  const currentSelectedProducts = selectList || selectedProducts.value
  
  if (currentSelectedProducts.length === 0) {
    return false
  }
  
  return currentProducts.every(product => {
    return isProductSelected(product, currentSelectedProducts)
  })
}

// 判断商品是否已被选中
const isProductSelected = (product: OrderSku, selectList?: OrderSku[]) => {
  const currentSelectedProducts = selectList || selectedProducts.value
  const result = currentSelectedProducts.filter(selectedProduct =>
    selectedProduct[props.itemIdentifier] === product[props.itemIdentifier])
  return !!result.length
}

// 判断商品是否被禁选
const isProductDisabled = (product: OrderSku) => {
  return props.disabledItems.some(disabledItem => 
    disabledItem[props.itemIdentifier] == product[props.itemIdentifier])
}

// 处理输入框值改变事件
const handleInputChange = (value: string) => {
  inputValue.value = value
  if (inputSelect.value === 'id') {
    searchParams.productIds = value
    searchParams.name = ''
  } else {
    searchParams.name = value
    searchParams.productIds = ''
  }
}

// 处理商品上下架状态改变事件
const handleApproveStatusChange = (value: number) => {
  searchParams.status = value
}

// 处理查询按钮点击事件
const handleSearch = async () => {
  const productList = await fetchData({ 
    ...searchParams, 
    pageNum: 1, 
    pageSize: paginationProps.pageSize 
  })
  selectAllChecked.value = isAllProductsSelected(productList)
}

// 处理重置按钮点击事件
const handleReset = async () => {
  inputSelect.value = 'id'
  inputValue.value = ''
  Object.assign(searchParams, initData)
  const productList = await fetchData({ 
    ...initData, 
    pageNum: 1, 
    pageSize: paginationProps.pageSize 
  })
  selectAllChecked.value = isAllProductsSelected(productList)
}

// 获取商品列表数据
const fetchData = async (params: any): Promise<OrderSku[]> => {
  loading.value = true
  try {
    const { data, total } = await getProductList(params)
    products.value = data || []
    paginationProps.total = total || 0
    return data || []
  } finally {
    loading.value = false
  }
}

// 处理商品选择事件
const handleProductSelect = (product: OrderSku) => {
  if (isProductDisabled(product)) {
    return
  }

  let updatedSelectedProducts: OrderSku[]

  if (props.max == 1) {
    if (selectedProducts.value.find(p => p[props.itemIdentifier] == product[props.itemIdentifier])) {
      updatedSelectedProducts = []
    } else {
      updatedSelectedProducts = [product]
    }
  } else {
    if (selectedProducts.value.find(p => p[props.itemIdentifier] == product[props.itemIdentifier])) {
      updatedSelectedProducts = selectedProducts.value.filter(p => p[props.itemIdentifier] !== product[props.itemIdentifier])
    } else {
      updatedSelectedProducts = [...selectedProducts.value, product]
    }

    if (updatedSelectedProducts.length > props.max) {
      ElMessage.error(`最多只能选择 ${props.max} 个商品。`)
      updatedSelectedProducts = updatedSelectedProducts.slice(0, props.max)
    }
  }

  selectedProducts.value = updatedSelectedProducts
  selectAllChecked.value = isAllProductsSelected(products.value, updatedSelectedProducts)
}

// 处理全选按钮点击事件
const handleSelectAll = (value: any) => {
  if (value) {
    const selectedProductsExcludingCurrentPage = selectedProducts.value.filter(selected =>
      !products.value.some(product => product[props.itemIdentifier] == selected[props.itemIdentifier])
    )

    const remainingQuota = props.max - selectedProductsExcludingCurrentPage.length

    const selectableProducts = products.value.filter(product => !isProductDisabled(product))

    if (remainingQuota <= 0) {
      ElMessage.error(`最多只能选择 ${props.max} 个商品。`)
      return
    }

    if (selectableProducts.length > remainingQuota) {
      const productsToSelect = selectableProducts.slice(0, remainingQuota)
      selectedProducts.value = [...selectedProductsExcludingCurrentPage, ...productsToSelect]
      selectAllChecked.value = false
      ElMessage.error(`已达到最大选择数量 ${props.max} 个，仅选择了当前页的 ${remainingQuota} 个商品。`)
    } else {
      selectedProducts.value = [...selectedProductsExcludingCurrentPage, ...selectableProducts]
      selectAllChecked.value = true
    }
  } else {
    const selectedProductsExcludingCurrentPage = selectedProducts.value.filter(selected =>
      !products.value.some(product => product[props.itemIdentifier] == selected[props.itemIdentifier])
    )

    selectedProducts.value = [...selectedProductsExcludingCurrentPage]
    selectAllChecked.value = false
  }
}

// 处理确定按钮点击事件
const handleConfirm = () => {
  emit('success', selectedProducts.value)
  emit('closeDialog')
}

// 分页处理
const handlePageChange = async (current: number) => {
  paginationProps.current = current
  const productList = await fetchData({
    ...searchParams,
    pageNum: current,
    pageSize: paginationProps.pageSize,
  })
  selectAllChecked.value = isAllProductsSelected(productList)
}

const handleSizeChange = async (pageSize: number) => {
  paginationProps.pageSize = pageSize
  paginationProps.current = 1
  const productList = await fetchData({
    ...searchParams,
    pageNum: 1,
    pageSize: pageSize,
  })
  selectAllChecked.value = isAllProductsSelected(productList)
}

const handleInputSelectChange = (value: string) => {
  inputSelect.value = value
  inputValue.value = ''
  searchParams.productIds = ''
  searchParams.name = ''
}

// 处理导入商品
const handleImportProducts = async () => {
  if (!importForm.skuIds.trim()) {
    ElMessage.error('请输入商品ID')
    return
  }
  
  importLoading.value = true
  try {
    const skuIds = importForm.skuIds.split(',').map(id => id.trim()).filter(id => id)
    const uniqueIds = Array.from(new Set(skuIds))
    
    if (uniqueIds.length === 0) {
      ElMessage.error('请输入有效的商品ID')
      return
    }
    
    const products = await getProductsByIds(uniqueIds)
    
    if (products.length === 0) {
      ElMessage.warning('未找到对应的商品')
      importResults.value = []
      return
    }
    
    const notFoundIds = uniqueIds.filter(id => !products.some(p => p.productId === id))
    if (notFoundIds.length > 0) {
      ElMessage.warning(`以下商品ID未找到：${notFoundIds.join(', ')}`)
    }
    
    importResults.value = products
    
    // 自动将导入的商品添加到已选商品中
    const newSelectedProducts = [...selectedProducts.value]
    products.forEach(product => {
      if (!isProductSelected(product, newSelectedProducts)) {
        if (newSelectedProducts.length < props.max) {
          newSelectedProducts.push(product)
        }
      }
    })
    
    if (newSelectedProducts.length > props.max) {
      ElMessage.warning(`最多只能选择 ${props.max} 个商品，已自动选择前 ${props.max} 个`)
      selectedProducts.value = newSelectedProducts.slice(0, props.max)
    } else {
      selectedProducts.value = newSelectedProducts
    }
    
    ElMessage.success(`成功导入 ${products.length} 个商品`)
    
    // 清空输入框
    importForm.skuIds = ''
  } catch (error) {
    ElMessage.error('导入商品失败')
  } finally {
    importLoading.value = false
  }
}

// 清空导入表单
const clearImportForm = () => {
  importForm.skuIds = ''
  importResults.value = []
}
</script>

<style scoped>
.product-content {
  height: 100%;
  width: 100%;
}

.product-tabs {
  height: 100%;
}

.import-panel {
  padding: 16px 0;
}

.import-grid {
  height: 300px;
  overflow-y: auto;
  grid-template-columns: repeat(5, 1fr);
  padding-right: 8px;
}

.import-results {
  margin-top: 24px;
}

.product-selected-num {
  color: var(--el-color-primary);
}

.product-grid-list {
  display: grid;
  flex: 1;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  overflow-y: auto;
  place-items: flex-start;
  height: 450px;
  padding-right: 8px;
}

.product-item {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 140px;
  padding: 8px;
  border: 1px solid #eee;
  border-radius: 12px;
}

.product-item.disabled {
  background-color: #f1f1f1;
  cursor: not-allowed;
}

.product-item.disabled .product-img {
  cursor: not-allowed;
}

.product-item.selected {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 5px var(--el-color-primary-light-5);
}

.product-img {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 122px;
  overflow: hidden;
  border-radius: 12px;
  cursor: pointer;
}

.product-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.checkbox {
  position: absolute;
  top: 5px;
  right: 5px;
}

.product-title {
  display: -webkit-box;
  width: 100%;
  height: 36px;
  margin: 6px 0;
  padding: 0 6px;
  overflow: hidden;
  color: rgb(17 17 17);
  font-size: 12px;
  line-height: 18px;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-info {
  display: flex;
  justify-content: space-between;
  padding: 0 6px;
}

.product-price {
  color: rgb(255 80 0);
  font-size: 16px;
}

.product-stock {
  display: flex;
  align-items: center;
  color: rgb(153 153 153);
  font-size: 12px;
}

.product-stock-num {
  margin-left: 5px;
}
</style>