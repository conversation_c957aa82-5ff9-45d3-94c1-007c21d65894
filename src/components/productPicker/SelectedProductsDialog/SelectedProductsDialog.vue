<template>
  <el-dialog
    v-model="dialogVisible"
    title="已选商品"
    width="850px"
    :close-on-click-modal="false"
    class="products-dialog"
    @close="$emit('close')"
  >
    <div class="product-content">
      <!-- 搜索栏 -->
      <el-row style="margin-bottom: 16px;">
        <el-space :size="10">
          <el-input
            v-model="searchValue"
            placeholder="请输入关键词或商品ID，多个商品用','分隔"
            style="width: 300px;"
            clearable
            @input="handleSearchChange"
          />
          <el-button @click="handleSearch">查询</el-button>
          <el-button
            @click="handleClearAll"
            :disabled="disabled"
          >
            清空全部商品
          </el-button>
        </el-space>
      </el-row>

      <div class="product-grid-list">
        <div
          v-for="product in pageData"
          :key="product.productId"
          class="product-item"
        >
          <div class="product-img">
            <img
              :src="product.skuMainPicture"
              :alt="product.skuName"
            />
            <!-- 删除按钮 -->
            <el-button
              class="delete-product-item-icon"
              type="primary"
              :disabled="disabled"
              text
              size="small"
              @click="() => handleRemove(product)"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <span class="product-title">{{ product.skuName }}</span>
          <div class="product-info">
            <span class="product-price"></span>
            <div class="product-stock">
              销量
              <span class="product-stock-num">{{ product.sellNum || 0 }}</span>
            </div>
          </div>
        </div>
      </div>

      <el-row 
        justify="space-between" 
        align="middle" 
        style="margin-top: 24px;"
      >
      <el-pagination
            v-model:current-page="pageNo"
            v-model:page-size="pageSize"
            :total="filteredList.length"
            :page-sizes="[10, 20, 30]"
            layout="total, sizes, prev, pager, next"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
          <el-space :size="10">
            <el-button @click="$emit('close')">取消</el-button>
            <el-button
              type="primary"
              :disabled="disabled"
              @click="handleSave"
            >
              保存
            </el-button>
          </el-space>
      </el-row>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElDialog, ElRow, ElCol, ElSpace, ElInput, ElButton, ElPagination, ElIcon } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'

interface OrderSku {
  productId: string
  skuName: string
  skuMainPicture: string
  sellNum?: number
  title?: string
  [key: string]: any
}

interface SelectedProductsDialogProps {
  items: OrderSku[]
  itemIdentifier: string
  disabled?: boolean
}

const props = withDefaults(defineProps<SelectedProductsDialogProps>(), {
  disabled: false
})

const emit = defineEmits<{
  save: [items: OrderSku[]]
  close: []
}>()

const dialogVisible = ref(true)
const searchValue = ref('')
const filteredList = ref<OrderSku[]>([...props.items])
const pageNo = ref(1)
const pageSize = ref(10)

// 根据 searchValue 对原列表进行过滤
const doFilter = (value: string, rawList: OrderSku[]) => {
  const words = value
    .split(',')
    .map(str => str.trim())
    .filter(Boolean)
  
  if (words.length === 0) {
    return rawList
  }
  
  return rawList.filter(item => {
    return words.some(word => {
      const productIdStr = String(item[props.itemIdentifier] || '')
      const titleStr = String(item.title || item.skuName || '')
      return productIdStr.includes(word) || titleStr.includes(word)
    })
  })
}

// 初始化时过滤
watch(() => props.items, (newItems) => {
  filteredList.value = doFilter(searchValue.value, newItems)
}, { immediate: true })

// 当用户输入查询关键字
const handleSearchChange = (value: string) => {
  searchValue.value = value
}

// 点击查询
const handleSearch = () => {
  const flist = doFilter(searchValue.value, props.items)
  filteredList.value = flist
  pageNo.value = 1
}

// 清空全部商品
const handleClearAll = () => {
  filteredList.value = []
  searchValue.value = ''
  pageNo.value = 1
}

// 删除单个商品
const handleRemove = (product: OrderSku) => {
  const updated = filteredList.value.filter(p => 
    p[props.itemIdentifier] !== product[props.itemIdentifier]
  )
  filteredList.value = updated
}

// 用户点击"保存"，将编辑后的列表传给父层
const handleSave = () => {
  emit('save', filteredList.value)
  emit('close')
}

// 分页处理
const startIndex = computed(() => (pageNo.value - 1) * pageSize.value)
const endIndex = computed(() => startIndex.value + pageSize.value)
const pageData = computed(() => filteredList.value.slice(startIndex.value, endIndex.value))

const handlePageChange = (page: number) => {
  pageNo.value = page
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  pageNo.value = 1
}
</script>

<style scoped>
.product-content {
  height: 100%;
  width: 100%;
}

.product-grid-list {
  display: grid;
  flex: 1;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  overflow-y: auto;
  place-items: flex-start;
  height: 450px;
  padding-right: 8px;
}

.product-item {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 140px;
  padding: 8px;
  border: 1px solid #eee;
  border-radius: 12px;
}

.delete-product-item-icon {
  position: absolute;
  top: 5px;
  right: 5px;
  color: #999;
}

.product-img {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 122px;
  height: 122px;
  overflow: hidden;
  border-radius: 12px;
  cursor: pointer;
}

.product-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-title {
  display: -webkit-box;
  width: 100%;
  height: 36px;
  margin: 6px 0;
  padding: 0 6px;
  overflow: hidden;
  color: rgb(17 17 17);
  font-size: 12px;
  line-height: 18px;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-info {
  display: flex;
  justify-content: space-between;
  padding: 0 6px;
}

.product-price {
  color: rgb(255 80 0);
  font-size: 16px;
}

.product-stock {
  display: flex;
  align-items: center;
  color: rgb(153 153 153);
  font-size: 12px;
}

.product-stock-num {
  margin-left: 5px;
}
</style>

<style>

.products-dialog .el-dialog__body {
  flex: 1;
}
</style>