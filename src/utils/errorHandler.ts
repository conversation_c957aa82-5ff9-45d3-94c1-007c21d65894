import { ElMessage } from 'element-plus';

/**
 * 统一错误处理函数
 * @param error 错误对象
 * @param customMessage 自定义错误消息
 */
export const handleError = (error: any, customMessage?: string) => {
  let errorMessage = customMessage;
  
  if (!errorMessage) {
    // 尝试从不同的错误对象结构中提取错误信息
    if (error?.resp_msg) {
      errorMessage = error.resp_msg;
    } else if (error?.message) {
      errorMessage = error.message;
    } else if (error?.data?.message) {
      errorMessage = error.data.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else {
      errorMessage = '操作失败，请稍后重试';
    }
  }
  
  ElMessage.error(errorMessage);
};

/**
 * 处理API调用错误的装饰器函数
 * @param apiCall API调用函数
 * @param options 配置选项
 */
export const withErrorHandling = async (
  apiCall: () => Promise<any>,
  options: {
    customMessage?: string;
    showGlobalError?: boolean; // 是否显示全局错误提示
    onError?: (error: any) => void; // 自定义错误处理回调
  } = {}
) => {
  try {
    return await apiCall();
  } catch (error) {
    if (options.onError) {
      options.onError(error);
    } else if (options.showGlobalError !== false) {
      handleError(error, options.customMessage);
    }
    throw error; // 重新抛出错误，让调用方可以继续处理
  }
};
