<template>
	<div class="">
		<a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
			<a-form-item label="活动名称" name="name">
				<a-input v-model:value="formData.name" class="with400" :maxlength="15" />
			</a-form-item>
			<a-form-item label="活动时间" name="rangeDate">
				<a-range-picker
					v-model:value="formData.rangeDate"
					show-time
					format="YYYY-MM-DD HH:mm:ss"
					valueFormat="YYYY-MM-DD HH:mm:ss"
					:disabled-date="disabledDate"
					:disabled="[disableEdit, false]"
				/>
			</a-form-item>
			<a-form-item label="报名条件限制">
				<a-form-item label="报名时间" name="signUpRangeDate" style="margin-top: 24px">
					<div style="margin-bottom: 8px; color: #666; font-size: 12px;">
						调试信息: signUpRangeDate = {{ JSON.stringify(formData.signUpRangeDate) }}
					</div>
					<a-range-picker
						v-model:value="formData.signUpRangeDate"
						show-time
						format="YYYY-MM-DD HH:mm:ss"
						valueFormat="YYYY-MM-DD HH:mm:ss"
						:disabled-date="disabledSignUpRangeDate"
						:disabled="[disableEdit, false]"
					/>
				</a-form-item>
				<a-form-item label="订单状态">
					<a-radio-group v-model:value="formData.orderStatus">
						<a-radio :value="1">已完成</a-radio>
					</a-radio-group>
				</a-form-item>
				<a-form-item label="老客订单时间" name="signDays">
					+  <a-input-number v-model:value="formData.signDays" placeholder="请输入校验天数" style="width: 120px" :min="disableEdit && props.defaultActData ? props.defaultActData.signDays : 1" :max="90" />  天
				</a-form-item>
				<a-form-item label="订单金额范围" required>
					<a-space align="baseline">
						<a-form-item name="signMinPrice" style="margin-bottom: 0">
							<a-input-number
								v-model:value="formData.signMinPrice"
								:min="0.01"
								:max="disableEdit && props.defaultActData ? props.defaultActData.signMinPrice : 99999"
								:precision="2"
								placeholder="最小金额"
								style="width: 120px"
								@change="() => formRef?.validateFields(['signMaxPrice'])"
							/>
						</a-form-item>
						<span>至</span>
						<a-form-item name="signMaxPrice" style="margin-bottom: 0">
							<a-input-number
								v-model:value="formData.signMaxPrice"
								:min="disableEdit && props.defaultActData ? props.defaultActData.signMaxPrice : 0.01"
								:max="99999"
								:precision="2"
								placeholder="最大金额"
								style="width: 120px"
								@change="() => formRef?.validateFields(['signMinPrice'])"
							/>
						</a-form-item>
						<span>元</span>
					</a-space>
				</a-form-item>
				<a-form-item label="活动商品">
					<a-radio-group v-model:value="formData.signGoodsType" :disabled="disableEdit">
						<a-radio :value="1">全部商品</a-radio>
						<a-radio :value="2">指定商品</a-radio>
					</a-radio-group>
				</a-form-item>
				<a-form-item label="指定商品" v-if="formData.signGoodsType === 2" name="signSkuListText">
					<a-textarea v-model:value="formData.signSkuListText" :autoSize="{ minRows: 4, maxRows: 10 }" placeholder="请输入指定商品" :disabled="disableEdit" />
					<div style="margin-top: 8px;color: #989898">请用英文逗号分隔各个指定商品SKU</div>
				</a-form-item>
				<a-form-item label="订单数量">
					<a-radio-group v-model:value="formData.signOrderNumType" :disabled="disableEdit">
						<a-radio :value="0">等于1笔（二回礼）</a-radio>
						<a-radio :value="1">大于等于1笔（老客礼）</a-radio>
					</a-radio-group>
				</a-form-item>
			</a-form-item>
			<a-form-item label="复购订单条件限制">
				<a-form-item label="订单状态" style="margin-top: 24px">
					<a-radio-group v-model:value="formData.orderStatus">
						<a-radio :value="1">已完成</a-radio>
					</a-radio-group>
				</a-form-item>
				<a-form-item label="订单时间" name="moreTimeRange">
					<a-range-picker
						v-model:value="formData.moreTimeRange"
						show-time
						format="YYYY-MM-DD HH:mm:ss"
						valueFormat="YYYY-MM-DD HH:mm:ss"
						:disabled="[disableEdit, false]"
						:disabled-date="disabledMoreTimeRangeDate"
					/>
				</a-form-item>
				<a-form-item label="订单金额范围" required>
					<a-space align="baseline">
						<a-form-item name="moreMinPrice" style="margin-bottom: 0">
							<a-input-number
								v-model:value="formData.moreMinPrice"
								:min="0.01"
								:max="disableEdit && props.defaultActData ? props.defaultActData.moreMinPrice : 99999"
								:precision="2"
								placeholder="最小金额"
								style="width: 120px"
								@change="() => formRef?.validateFields(['moreMaxPrice'])"
							/>
						</a-form-item>
						<span>至</span>
						<a-form-item name="moreMaxPrice" style="margin-bottom: 0">
							<a-input-number
								v-model:value="formData.moreMaxPrice"
								:min="disableEdit && props.defaultActData ? props.defaultActData.moreMaxPrice : 0.01"
								:max="99999"
								:precision="2"
								placeholder="最大金额"
								style="width: 120px"
								@change="() => formRef?.validateFields(['moreMinPrice'])"
							/>
						</a-form-item>
						<span>元</span>
					</a-space>
				</a-form-item>
				<a-form-item label="活动商品">
					<a-radio-group v-model:value="formData.orderGoodsType" :disabled="disableEdit">
						<a-radio :value="1">全部商品</a-radio>
						<a-radio :value="2">指定商品</a-radio>
					</a-radio-group>
				</a-form-item>
				<a-form-item label="指定商品" v-if="formData.orderGoodsType === 2" name="orderSkuListText">
					<a-textarea v-model:value="formData.orderSkuListText" :autoSize="{ minRows: 4, maxRows: 10 }" placeholder="请输入指定商品" :disabled="disableEdit" />
					<div style="margin-top: 8px;color: #989898">请用英文逗号分隔各个指定商品SKU</div>
				</a-form-item>
				<a-form-item label="订单数量">
					<a-radio-group :value="1">
						<!-- <a-radio :value="0">等于1笔</a-radio> -->
						<a-radio :value="1" disabled>大于等于1笔</a-radio>
					</a-radio-group>
				</a-form-item>
				<a-form-item label="订单延迟天数" name="moreDays">
					确认收货
					<a-input-number v-model:value="formData.moreDays" :min="0" :max="15" style="width: 120px" :disabled="disableEdit" />
					天后计入订单
				</a-form-item>
			</a-form-item>

			<a-form-item label="奖品设置">
				<a-table :columns="disableEdit ? columns : columns.filter((item) => item.key !== 'operation')" :data-source="formData.prizeList" :pagination="false" bordered>
					<template #bodyCell="{ column, index: rowIndex }">
						<template v-if="column.key === 'prizeName'">
							<a-form-item :name="['prizeList', rowIndex, 'prizeName']" :rules="{ required: true, message: '请输入奖品名称', trigger: 'change' }">
								<a-input v-model:value="formData.prizeList[rowIndex].prizeName" :maxlength="15" />
							</a-form-item>
						</template>
						<template v-if="column.key === 'sendTotalCount'">
							<a-form-item :name="['prizeList', rowIndex, 'sendTotalCount']" :rules="{ required: true, message: '请输入奖品总数量', trigger: 'change' }">
								<a-input-number :min="1" :max="99999" v-model:value="formData.prizeList[rowIndex].sendTotalCount" />
							</a-form-item>
						</template>
						<template v-if="column.key === 'prizeImg'">
							<a-form-item :name="['prizeList', rowIndex, 'prizeImg']" :rules="{ required: true, message: '请上传奖品图', trigger: 'change' }">
								<MyUpload
									@handleAvatarSuccess="
										(data) => {
											formData.prizeList[rowIndex].prizeImg = data.res.data;
											formRef.validate([['prizeList', rowIndex, 'prizeImg']]);
										}
									"
									:imageUrl="formData.prizeList[rowIndex].prizeImg"
									:size="1"
									class="hide-input"
								>
									<template #remark>
										<div>图片尺寸建议 宽750</div>
									</template>
								</MyUpload>
							</a-form-item>
						</template>
						<template v-if="column.key === 'operation'">
							<a-space>
								<a-button type="primary" @click="handleDeletePrize(rowIndex)">删除</a-button>
							</a-space>
						</template>
					</template>
				</a-table>
			</a-form-item>
			<a-form-item label="曝光商品" name="exposureSkuListText">
				<a-textarea v-model:value="formData.exposureSkuListText" :autoSize="{ minRows: 4, maxRows: 10 }" placeholder="请输入曝光商品" />
				<div style="margin-top: 8px;color: #989898">请用英文逗号分隔各个曝光商品SKU</div>
			</a-form-item>
			<a-form-item label="活动规则" name="rule">
				<a-textarea v-model:value="formData.rule" :autoSize="{ minRows: 4, maxRows: 10 }" placeholder="请输入活动规则" />
			</a-form-item>
		</a-form>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import MyUpload from '/@/components/MyUpload.vue';
import { useRoute } from 'vue-router';
import { dayjs } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { exportSkuTemplate, importSkuExcel } from '/@/utils/request/api/JBAT/repurchaseGift';
import { downloadExcel } from '/@/utils';

const route = useRoute();
const props = defineProps(['actData', 'defaultActData']);

const formRef = ref();

const isEdit = ref(route.query.type === 'edit');
const disableEdit = ref(false);
const isBeforeStart = ref(true);

const checkActStatus = () => {
	const now = dayjs();
	const startTime = dayjs(props.actData.rangeDate[0]);
	isBeforeStart.value = now.isBefore(startTime);
	disableEdit.value = isEdit.value && !isBeforeStart.value;
};

const columns = [
	{
		title: '奖品名称',
		dataIndex: 'prizeName',
		key: 'prizeName'
	},
	{
		title: '奖品数量',
		dataIndex: 'sendTotalCount',
		key: 'sendTotalCount'
	},
	{
		title: '奖品图',
		dataIndex: 'prizeImg',
		key: 'prizeImg'
	},
	{
		title: '操作',
		dataIndex: 'operation',
		key: 'operation'
	}
];

const rules: Record<string, Rule[]> = {
	name: [{ required: true, message: '请输入活动名称', trigger: 'change' }],
	rangeDate: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
	signUpRangeDate: [
		{
			required: true,
			trigger: 'change',
			validator: (rule, value) => {
				if (value && Array.isArray(value) && value[0] && value[1]) {
					if (disableEdit.value && props.defaultActData && props.defaultActData.signUpRangeDate && props.defaultActData.signUpRangeDate[1] && dayjs(value[1]).isBefore(dayjs(props.defaultActData.signUpRangeDate[1]))) {
						return Promise.reject('报名时间结束时间只能延后');
					} else {
						return Promise.resolve();
					}
				} else {
					return Promise.reject('请选择报名时间');
				}
			}
		}
	],
	signMinPrice: [
		{ required: true, message: '请输入报名订单金额最小值', trigger: 'change' },
		{
			validator: (rule, value) => {
				if (value && formData.value.signMaxPrice && Number(value) >= Number(formData.value.signMaxPrice)) {
					return Promise.reject('报名订单金额下限必须小于上限');
				} else {
					return Promise.resolve();
				}
			},
			trigger: 'change'
		}
	],
	signMaxPrice: [
		{ required: true, message: '请输入报名订单金额最大值', trigger: 'change' },
		{
			validator: (rule, value) => {
				if (value && formData.value.signMinPrice && Number(value) <= Number(formData.value.signMinPrice)) {
					return Promise.reject('报名订单金额上限必须大于下限');
				} else {
					return Promise.resolve();
				}
			},
			trigger: 'change'
		}
	],
	signDays: [{ required: true, message: '请输入老客订单时间', trigger: 'change' }],
	signSkuListText: [{ required: true, message: '请输入报名指定商品', trigger: 'change' }],
	moreTimeRange: [
		{
			required: true,
			trigger: 'change',
			validator: (rule, value) => {
				if (value && Array.isArray(value) && value[0] && value[1]) {
					if (disableEdit.value && props.defaultActData && props.defaultActData.moreTimeRange && props.defaultActData.moreTimeRange[1] && dayjs(value[1]).isBefore(dayjs(props.defaultActData.moreTimeRange[1]))) {
						return Promise.reject('复购订单时间结束时间只能延后');
					} else {
						return Promise.resolve();
					}
				} else {
					return Promise.reject('请选择复购订单时间');
				}
			}
		}
	],
	moreMinPrice: [
		{ required: true, message: '请输入订单金额最小值', trigger: 'change' },
		{
			validator: (rule, value) => {
				if (value && formData.value.moreMaxPrice && Number(value) >= Number(formData.value.moreMaxPrice)) {
					return Promise.reject('复购订单金额下限必须小于上限');
				} else {
					return Promise.resolve();
				}
			},
			trigger: 'change'
		}
	],
	moreMaxPrice: [
		{ required: true, message: '请输入订单金额最大值', trigger: 'change' },
		{
			validator: (rule, value) => {
				if (value && formData.value.moreMinPrice && Number(value) <= Number(formData.value.moreMinPrice)) {
					return Promise.reject('复购订单金额上限必须大于下限');
				} else {
					return Promise.resolve();
				}
			},
			trigger: 'change'
		}
	],
	moreDays: [{ required: true, message: '请输入订单延迟天数', trigger: 'change' }],
	orderSkuListText: [{ required: true, message: '请输入复购指定商品', trigger: 'change' }],
	exposureSkuListText: [{ required: true, message: '请输入曝光商品', trigger: 'change' }],
	rule: [{ required: true, message: '请输入活动规则', trigger: 'change' }]
};

const defaultPrize = {
	prizeName: '',
	sendTotalCount: '',
	prizeImg: ''
};

const formData = ref({
	shopId: '',
	name: '',
	rangeDate: ['', ''] as [string, string],
	signUpRangeDate: ['', ''] as [string, string],
	orderStatus: 1,
	signOrderNumType: 1,
	signMinPrice: '',
	signMaxPrice: '',
	signGoodsType: 1,
	signSkuListText: '',
	moreTimeRange: ['', ''] as [string, string],
	moreMinPrice: '',
	moreMaxPrice: '',
	orderGoodsType: 1,
	orderSkuListText: '',
	moreDays: '',
	prizeList: [{ ...defaultPrize }],
	signDays: '',
	exposureSkuListText: '',
	rule: ''
});

const disabledDate = (current) => {
	return dayjs(current).isBefore(dayjs().startOf('day'));
};

const disabledSignUpRangeDate = (current) => {
	try {
		// 如果活动时间未选择或为null，只禁用今天之前的日期
		if (!formData.value.rangeDate || !Array.isArray(formData.value.rangeDate) || !formData.value.rangeDate[0] || !formData.value.rangeDate[1]) {
			const isDisabled = dayjs(current).isBefore(dayjs().startOf('day'));
			console.log('活动时间为空，禁用今天之前的日期:', isDisabled);
			return isDisabled;
		}
		// 获取活动开始和结束日期（只比较日期，不比较时间）
		const activityStartDate = dayjs(formData.value.rangeDate[0]).startOf('day');
		const activityEndDate = dayjs(formData.value.rangeDate[1]).startOf('day');
		const currentDate = dayjs(current).startOf('day');
		// 报名时间应该在活动时间范围内（包含边界）
		// 禁用：小于活动开始日期 或 大于活动结束日期
		const isDisabled = currentDate.isBefore(activityStartDate) || currentDate.isAfter(activityEndDate);
		return isDisabled;
	} catch (error) {
		console.error('disabledSignUpRangeDate 错误:', error, 'rangeDate:', formData.value.rangeDate);
		return dayjs(current).isBefore(dayjs().startOf('day'));
	}
};

const disabledMoreTimeRangeDate = (current) => {
	try {
		if (!formData.value.rangeDate || !Array.isArray(formData.value.rangeDate) || !formData.value.rangeDate[0] || !formData.value.rangeDate[1]) {
			return dayjs(current).isBefore(dayjs().startOf('day'));
		}
		return dayjs(current).startOf('day').isAfter(dayjs(formData.value.rangeDate[1]));
	} catch (error) {
		console.error('disabledMoreTimeRangeDate 错误:', error, 'rangeDate:', formData.value.rangeDate);
		return dayjs(current).isBefore(dayjs().startOf('day'));
	}
};

const handleDeletePrize = (index: number) => {
	formData.value.prizeList[index] = JSON.parse(JSON.stringify(defaultPrize));
};

const onSubmit = async () => {
	try {
		if (!formRef.value) {
			throw new Error('表单引用未初始化');
		}
		await formRef.value.validate();
		return Promise.resolve(formData.value);
	} catch (error) {
		return Promise.reject(error);
	}
};

const getFormData = () => {
	return formData.value;
};

const isReady = () => {
	return !!formRef.value;
};

checkActStatus();

// 监听活动时间变化，仅用于调试，不做任何自动清空操作
watch(
	() => formData.value.rangeDate,
	(newRangeDate, oldRangeDate) => {
		console.log('活动时间变化触发 watch:', {
			old: oldRangeDate,
			new: newRangeDate,
			signUpRangeDate: formData.value.signUpRangeDate
		});

		// 不做任何自动清空操作，让用户手动管理报名时间
		console.log('活动时间改变，保持报名时间不变');
	},
	{ deep: true, immediate: false }
);

// 只在组件初始化时设置一次数据，绝对不使用 effect
if (props.actData) {
	console.log('组件初始化，设置 formData:', props.actData);
	console.log('props.actData.signUpRangeDate:', props.actData.signUpRangeDate);
	console.log('formData.value.signUpRangeDate before:', formData.value.signUpRangeDate);

	formData.value = {
		...formData.value,
		...props.actData
	};

	console.log('formData.value.signUpRangeDate after:', formData.value.signUpRangeDate);
}

defineExpose({
	onSubmit,
	getFormData,
	isReady
});
</script>

<style scoped lang="scss">
.with400 {
	width: 400px;
}
</style>
<style>
.hide-input input {
	display: none !important;
}
</style>
