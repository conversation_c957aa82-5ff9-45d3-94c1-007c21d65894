<template>
	<div class="flex">
		<div class="form-box">
			<a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
				<a-form-item label="kv图" name="pageBg">
					<MyUpload @handleAvatarSuccess="handleKvImg" :imageUrl="formData.pageBg" :size="1" class="hide-input">
						<template #remark>
							<div>图片尺寸建议 宽750</div>
						</template>
					</MyUpload>
				</a-form-item>
				<a-form-item label="领取攻略图" name="actStrategy">
					<MyUpload v-if="isHaipu" @handleAvatarSuccess="handleStrategyImg" :imageUrl="formData.actStrategy" :size="1" class="hide-input" imgWidth="700" imgHeight="240"> </MyUpload>
					<MyUpload v-else @handleAvatarSuccess="handleStrategyImg" :imageUrl="formData.actStrategy" :size="1" class="hide-input" imgWidth="730" imgHeight="335"> </MyUpload>
				</a-form-item>
				<a-form-item label="报名成功弹窗图" name="signSuccessPopup">
					<MyUpload v-if="isHaipu" @handleAvatarSuccess="handleSignSuccessPopup" :imageUrl="formData.signSuccessPopup" :size="1" class="hide-input" imgWidth="698" imgHeight="636"> </MyUpload>
					<MyUpload v-else @handleAvatarSuccess="handleSignSuccessPopup" :imageUrl="formData.signSuccessPopup" :size="1" class="hide-input" imgWidth="615" imgHeight="569"> </MyUpload>
				</a-form-item>
				<a-form-item label="报名失败弹窗图" name="signFailPopup">
					<MyUpload v-if="isHaipu" @handleAvatarSuccess="handleSignFailPopup" :imageUrl="formData.signFailPopup" :size="1" class="hide-input" imgWidth="698" imgHeight="938"> </MyUpload>
					<MyUpload v-else @handleAvatarSuccess="handleSignFailPopup" :imageUrl="formData.signFailPopup" :size="1" class="hide-input" imgWidth="631" imgHeight="580"> </MyUpload>
				</a-form-item>
				<a-form-item label="符合条件订单=0">
					<a-form-item label="报名失败弹窗跳转类型">
						<a-radio-group v-model:value="formData.signFailBtnType0" @change="formData.signFailBtnId0 = ''">
							<a-radio :value="1">店铺首页</a-radio>
							<a-radio :value="2">店铺自定义页</a-radio>
<!--							<a-radio :value="3">新客礼1.0</a-radio>-->
<!--							<a-radio :value="4">新客礼2.0</a-radio>-->
<!--							<a-radio :value="5">集罐礼</a-radio>-->
						</a-radio-group>
					</a-form-item>
					<a-form-item label="报名失败弹窗跳转id" name="signFailBtnId0" v-if="formData.signFailBtnType0 >= 2">
						<a-input v-model:value="formData.signFailBtnId0" placeholder="请输入跳转id" class="with400" />
					</a-form-item>
				</a-form-item>
				<a-form-item label="符合条件订单>1">
					<a-form-item label="报名失败弹窗跳转类型">
						<a-radio-group v-model:value="formData.signFailBtnType1" @change="formData.signFailBtnId1 = ''">
							<a-radio :value="1">店铺首页</a-radio>
							<a-radio :value="2">店铺自定义页</a-radio>
<!--							<a-radio :value="3">新客礼1.0</a-radio>-->
<!--							<a-radio :value="4">新客礼2.0</a-radio>-->
<!--							<a-radio :value="5">集罐礼</a-radio>-->
						</a-radio-group>
					</a-form-item>
					<a-form-item label="报名失败弹窗跳转id" name="signFailBtnId1" v-if="formData.signFailBtnType1 >= 2">
						<a-input v-model:value="formData.signFailBtnId1" placeholder="请输入跳转id" class="with400" />
					</a-form-item>
				</a-form-item>
				<a-form-item label="领取失败弹窗图" name="drawFailPopup">
					<MyUpload v-if="isHaipu" @handleAvatarSuccess="handleDrawFailPopup" :imageUrl="formData.drawFailPopup" :size="1" class="hide-input" imgWidth="698" imgHeight="886"></MyUpload>
					<MyUpload v-else @handleAvatarSuccess="handleDrawFailPopup" :imageUrl="formData.drawFailPopup" :size="1" class="hide-input" imgWidth="631" imgHeight="607"> </MyUpload>
				</a-form-item>
				<a-form-item label="领取失败跳转类型">
					<a-radio-group v-model:value="formData.drawFailBtnType" @change="formData.drawFailBtnId = ''">
						<a-radio :value="1">店铺首页</a-radio>
						<a-radio :value="2">店铺自定义页</a-radio>
<!--						<a-radio :value="3">新客礼1.0</a-radio>-->
<!--						<a-radio :value="4">新客礼2.0</a-radio>-->
<!--						<a-radio :value="5">集罐礼</a-radio>-->
					</a-radio-group>
				</a-form-item>
				<a-form-item label="领取失败跳转id" name="drawFailBtnId" v-if="formData.drawFailBtnType >= 2">
					<a-input v-model:value="formData.drawFailBtnId" placeholder="请输入跳转id" class="with400" />
				</a-form-item>
			</a-form>
		</div>
		<LzPhone>
			<slot>
				<div class="phone-bk" :style="{ backgroundImage: `url(${formData.pageBg})` }">
					<img class="phoneNumBk" :src="formData.actStrategy" alt="" />
				</div>
			</slot>
		</LzPhone>
	</div>
</template>

<script lang="ts" setup>
import { effect, ref } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import MyUpload from '/@/components/MyUpload.vue';
import { useRoute } from 'vue-router';
const route = useRoute();

const formRef = ref();

const props = defineProps(['decoData']);
const HAIPUShopList = [
	{ label: '海普诺凯1897官方旗舰店', value: '37702451' },
	{ label: '能立多旗舰店', value: '40842709' }
];
const isHaipu = ref(false);
const checkDecoSize = () => {
	if (HAIPUShopList.find((item: any) => item.value === route.query.shopId)) {
		isHaipu.value = true;
	}
};

const rules: Record<string, Rule[]> = {
	pageBg: [{ required: true, message: '请上传kv图', trigger: 'change' }],
	strategyImg: [{ required: true, message: '请上传领取攻略图', trigger: 'change' }],
	speakerImg: [{ required: true, message: '请上传不满足页kv', trigger: 'change' }],
	signSuccessPopup: [{ required: true, message: '请上传报名成功弹窗图', trigger: 'change' }],
	signFailPopup: [{ required: true, message: '请上传报名失败弹窗图', trigger: 'change' }],
	signFailBtnId0: [{ required: true, message: '请输入跳转id', trigger: 'change' }],
	signFailBtnId1: [{ required: true, message: '请输入跳转id', trigger: 'change' }],
	drawFailPopup: [{ required: true, message: '请上传领取失败弹窗图', trigger: 'change' }],
	drawFailBtnId: [{ required: true, message: '请输入跳转id', trigger: 'change' }]
};

const formData = ref({
	pageBg: '',
	actStrategy: '',
	signSuccessPopup: '',
	signFailPopup: '',
	drawFailPopup: '',
	signFailBtnType0: 1,
	signFailBtnId0: '',
	signFailBtnType1: 1,
	signFailBtnId1: '',
	drawFailBtnType: 1,
	drawFailBtnId: ''
});

const handleKvImg = (data: any) => {
	formData.value.pageBg = data.res.data;
	formRef.value.validate(['kvImg']);
};
const handleStrategyImg = (data: any) => {
	formData.value.actStrategy = data.res.data;
	formRef.value.validate(['strategyImg']);
};
const handleSignSuccessPopup = (data: any) => {
	formData.value.signSuccessPopup = data.res.data;
	formRef.value.validate(['signSuccessPopup']);
};
const handleSignFailPopup = (data: any) => {
	formData.value.signFailPopup = data.res.data;
	formRef.value.validate(['signFailPopup']);
};
const handleDrawFailPopup = (data: any) => {
	formData.value.drawFailPopup = data.res.data;
	formRef.value.validate(['drawFailPopup']);
};

const onSubmit = async () => {
	try {
		await formRef.value.validate();
		return Promise.resolve(formData.value);
	} catch (error) {
		return Promise.reject(error);
	}
};

effect(() => {
	formData.value = props.decoData;
	checkDecoSize();
});

defineExpose({
	onSubmit
});
</script>

<style scoped lang="scss">
.flex {
	display: flex;
}
.form-box {
	flex: 1;
}
.with400 {
	width: 400px;
}
.phone-bk {
	width: 337.5px;
	background-repeat: no-repeat;
	background-size: 100%;
	padding-top: 310px;
	padding-bottom: 40px;
	.phoneNumBk {
		width: 337.5px - 20px;
		display: block;
		margin: 0 auto 20px;
	}
	.strategyImg {
		width: 337.5px - 20px;
		display: block;
		margin: 0 auto;
	}
}
</style>
<style>
.hide-input input {
	display: none !important;
}
</style>
