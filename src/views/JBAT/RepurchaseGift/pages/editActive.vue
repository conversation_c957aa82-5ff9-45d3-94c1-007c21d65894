<template>
	<div class="zst-main edit-page">
		<a-spin :spinning="isLoading">
			<div class="scroll-container">
				<DecoPage ref="decoRef" :decoData="decoData" v-if="step === 1" />
				<ActiveInfoPage ref="activeInfoRef" :actData="actData" :defaultActData="defaultActData" v-else-if="step === 2" />
			</div>
		</a-spin>

		<div class="edit-page-footer">
			<a-button @click="goBack" v-if="step === 1">取消</a-button>
			<a-button v-if="step !== 1" @click="prevStep">上一步</a-button>
			<a-button type="primary" v-if="step !== 2" @click="nexStep">下一步</a-button>
			<a-button type="primary" v-if="step === 2 && !isEdit" @click="confirmForm">创建活动</a-button>
			<a-button type="primary" v-if="step === 2 && isEdit" @click="confirmForm">保存活动</a-button>
		</div>

		<a-modal :visible="visible" title="确认" :confirm-loading="confirmLoading" @ok="createAct" @cancel="onCancel">
			<p>是否确认{{ isEdit ? '保存' : '创建' }}活动？</p>
		</a-modal>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import DecoPage from './components/DecoPage.vue';
import ActiveInfoPage from './components/ActiveInfoPage.vue';
import { addActivity, updateActivity, getActivityDetail } from '/@/utils/request/api/JBAT/repurchaseGift';
import { dayjs } from 'element-plus';
import { ElMessage } from 'element-plus';
import { Message } from '@element-plus/icons-vue';

const route = useRoute();
const router = useRouter();

const isEdit = ref(route.query.type === 'edit');
const isLoading = ref(false);
const visible = ref(false);
const confirmLoading = ref(false);
const step = ref(1);
const decoRef = ref();
const activeInfoRef = ref();

const HAIPUShopList = [
	{ label: '海普诺凯1897官方旗舰店', value: '37702451' },
	{ label: '能立多旗舰店', value: '40842709' }
];

const defaultData = {
	pageBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/316961/3/13095/158084/686736d3F91f481e5/7dbd93e96e53a714.jpg',
	actStrategy: 'https://img10.360buyimg.com/imgzone/jfs/t1/287877/35/19959/60958/686736d6F0806023e/f9173d88b958168c.png',
	signSuccessPopup: 'https://img10.360buyimg.com/imgzone/jfs/t1/304258/33/16255/62262/686b3279Fdd1f1424/a89d3d1797d1adba.png',
	signFailPopup: 'https://img10.360buyimg.com/imgzone/jfs/t1/288426/5/12743/43250/686b3278F76b991ee/02ef8cb1e8a130e7.png',
	drawFailPopup: 'https://img10.360buyimg.com/imgzone/jfs/t1/315190/35/15148/48987/686b3278Faf0f4745/f889bc47042ffd53.png',
	signFailBtnType0: 1,
	signFailBtnId0: '',
	signFailBtnType1: 1,
	signFailBtnId1: '',
	drawFailBtnType: 1,
	drawFailBtnId: ''
};

const HAiPUDefaultData = {
	pageBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/314857/24/15259/112814/686ccb26Fc80321b8/0c1574ec4d1b321e.png',
	actStrategy: 'https://img10.360buyimg.com/imgzone/jfs/t1/296255/12/21231/18972/686ccb27F79324cc8/b77ba8b0bdfb1574.png',
	signSuccessPopup: 'https://img10.360buyimg.com/imgzone/jfs/t1/316682/2/14360/43863/686ccb9aF80bcafd7/d37938c3622cd7ed.png',
	signFailPopup: 'https://img10.360buyimg.com/imgzone/jfs/t1/311577/26/15061/48474/686ccb9aF93401744/2a8d29e08749cc7e.png',
	drawFailPopup: 'https://img10.360buyimg.com/imgzone/jfs/t1/286238/32/14725/53131/686ccb99F1e9049de/6cb51bf549a98738.png',
	signFailBtnType0: 1,
	signFailBtnId0: '',
	signFailBtnType1: 1,
	signFailBtnId1: '',
	drawFailBtnType: 1,
	drawFailBtnId: ''
};

const decoData = ref({
	kvImg: '',
	strategyImg: '',
	speakerImg: '',
	linkType: 1
});
const defaultPrize = {
	prizeName: '',
	sendTotalCount: '',
	prizeImg: ''
};
const actData = ref({
	shopId: '',
	name: `复购礼-${dayjs().format('YYYY-MM-DD')}`,
	rangeDate: [dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), dayjs().add(30, 'day').endOf('day').format('YYYY-MM-DD HH:mm:ss')], // 活动时间
	signUpRangeDate: [], // 报名时间
	orderStatus: 1, // 订单状态
	signDays: '', // 老客订单时间
	signOrderNumType: 1, // 订单数量
	signMinPrice: '', // 订单金额范围最小值
	signMaxPrice: '', // 订单金额范围最大值
	signGoodsType: 1, // 报名商品 1：全部商品 2：指定商品
	signSkuListText: '', // 报名商品
	moreTimeRange: [], // 复购订单时间
	moreMinPrice: '', // 复购订单金额最小值
	moreMaxPrice: '', // 复购订单金额最大值
	orderGoodsType: 1, // 复购商品 1：全部商品 2：指定商品
	orderSkuListText: '', // 复购商品
	moreDays: '', // 复购订单延迟天数
	prizeList: [{ ...defaultPrize }], // 奖品列表
	exposureSkuListText: '', // 曝光商品
	rule: '' // 活动规则
});

const defaultActData = ref({});

const nexStep = async () => {
	try {
		if (step.value === 1) {
			decoData.value = await decoRef.value.onSubmit();
			step.value = 2;
		}
	} catch (error) {
		console.error(error);
	}
};
// 上一步
const prevStep = () => {
	actData.value = activeInfoRef.value.getFormData();
	step.value = step.value - 1;
	console.log('actData', actData.value);
};

// 新增方法：将字符串转换为数组，支持多种分隔符
const stringToArray = (str: string) => {
	if (!str || typeof str !== 'string') {
		return [];
	}

	// 使用正则表达式匹配多种分隔符：中英文逗号、空格、换行符、制表符、分号等
	const separatorRegex = /[,，\s\n\r\t;；]+/;

	// 分割字符串并过滤空值
	return str
		.split(separatorRegex)
		.map((item) => item.trim())
		.filter((item) => item.length > 0)
		.map((item) => ({
			skuId: item
		}));
};

const getParams = (): any => {
	return {
		decoData: JSON.stringify(decoData.value),
		shopId: actData.value.shopId,
		activityName: actData.value.name,
		startTime: dayjs(actData.value.rangeDate[0]).format('YYYY-MM-DD HH:mm:ss'),
		endTime: dayjs(actData.value.rangeDate[1]).format('YYYY-MM-DD HH:mm:ss'),
		signStartTime: dayjs(actData.value.signUpRangeDate[0]).format('YYYY-MM-DD HH:mm:ss'),
		signEndTime: dayjs(actData.value.signUpRangeDate[1]).format('YYYY-MM-DD HH:mm:ss'),
		signDays: actData.value.signDays,
		signOrderNumType: actData.value.signOrderNumType,
		signMinPrice: actData.value.signMinPrice,
		signMaxPrice: actData.value.signMaxPrice,
		signGoodsType: actData.value.signGoodsType,
		moreStartTime: dayjs(actData.value.moreTimeRange[0]).format('YYYY-MM-DD HH:mm:ss'),
		moreEndTime: dayjs(actData.value.moreTimeRange[1]).format('YYYY-MM-DD HH:mm:ss'),
		moreMinPrice: actData.value.moreMinPrice,
		moreMaxPrice: actData.value.moreMaxPrice,
		orderGoodsType: actData.value.orderGoodsType,
		moreDays: actData.value.moreDays,
		signSkuList: stringToArray(actData.value.signSkuListText),
		orderSkuList: stringToArray(actData.value.orderSkuListText),
		exposureSkuList: stringToArray(actData.value.exposureSkuListText),
		prizeList: actData.value.prizeList,
		rule: actData.value.rule
	};
};

function hasDuplicate(arr) {
	return new Set(arr).size !== arr.length;
}

const createAct = async () => {
	const params = getParams();
	try {
		confirmLoading.value = true;
		if (route.query.type === 'create') {
			params.shopId = route.query.shopId;
			// 判断各个skuList里是否有重复sku
			if (!hasDuplicate(params.signSkuList)) {
				ElMessage.error('报名商品不能重复');
				return;
			}
			if (!hasDuplicate(params.orderSkuList)) {
				ElMessage.error('复购商品不能重复');
				return;
			}
			if (!hasDuplicate(params.exposureSkuList)) {
				ElMessage.error('曝光商品不能重复');
				return;
			}
			await addActivity(params);
		} else {
			await updateActivity({ activityId: route.query.id, ...params });
		}
		confirmLoading.value = false;
		visible.value = false;
		router.go(-1);
	} catch (error) {
		confirmLoading.value = false;
		console.error(error);
	}
};

const onCancel = () => {
	if (confirmLoading.value) return;
	visible.value = false;
};

const confirmForm = async () => {
	try {
		actData.value = await activeInfoRef.value.onSubmit();
		if (dayjs(actData.value.signUpRangeDate[0]).isBefore(actData.value.rangeDate[0]) || dayjs(actData.value.signUpRangeDate[1]).isAfter(actData.value.rangeDate[1])) {
			ElMessage.error('报名时间需在活动时间内');
			return;
		}
		if (actData.value.signMinPrice > actData.value.signMaxPrice) {
			ElMessage.error('报名订单金额范围最小值需小于最大值');
			return;
		}
		if (dayjs(actData.value.moreTimeRange[1]).isAfter(actData.value.rangeDate[1])) {
			ElMessage.error('复购订单结束时间需在活动结束时间前');
			return;
		}
		if (dayjs(actData.value.moreTimeRange[1]).isBefore(actData.value.signUpRangeDate[1])) {
			ElMessage.error('复购订单结束时间需在报名结束时间后');
			return;
		}
		if (actData.value.moreMinPrice > actData.value.moreMaxPrice) {
			ElMessage.error('复购订单金额范围最小值需小于最大值');
			return;
		}
		visible.value = true;
	} catch (error) {
		console.error(error);
	}
};

const goBack = () => {
	router.go(-1);
};

const init = async () => {
	if (route.query.type === 'create') {
		if (HAIPUShopList.find((item: any) => item.value === route.query.shopId)) {
			decoData.value = JSON.parse(JSON.stringify(HAiPUDefaultData));
		} else {
			decoData.value = JSON.parse(JSON.stringify(defaultData));
		}
		return;
	}
	try {
		const data = (await getActivityDetail({ activityId: route.query.id })) as any;

		actData.value = {
			shopId: data.shopId,
			name: data.activityName,
			rangeDate: [data.startTime, data.endTime],
			signUpRangeDate: [data.signStartTime, data.signEndTime],
			orderStatus: 1,
			signDays: data.signDays,
			signOrderNumType: data.signOrderNumType,
			signMinPrice: data.signMinPrice,
			signMaxPrice: data.signMaxPrice,
			signGoodsType: data.signGoodsType !== undefined ? data.signGoodsType : (data.signSkuList.length === 0 ? 1 : 2),
			signSkuListText: data.signSkuList,
			moreTimeRange: [data.moreStartTime, data.moreEndTime],
			moreMinPrice: data.moreMinPrice,
			moreMaxPrice: data.moreMaxPrice,
			orderGoodsType: data.orderGoodsType !== undefined ? data.orderGoodsType : (data.orderSkuList.length === 0 ? 1 : 2),
			orderSkuListText: data.orderSkuList,
			moreDays: data.moreDays,
			prizeList: data.prizeList,
			exposureSkuListText: data.exposureSkuList,
			rule: data.rule
		};
		defaultActData.value = JSON.parse(JSON.stringify(actData.value));

		decoData.value = JSON.parse(data.decoData);
	} catch (error) {
		console.error(error);
	}
};
init();
</script>

<style scoped lang="scss">
.edit-page {
	position: relative;
	// background: #f0f2f5;

	.scroll-container {
		height: calc(100vh - 140px);
		overflow: auto;
	}
	.edit-page-footer {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 70px;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #fff;
	}
}
</style>
