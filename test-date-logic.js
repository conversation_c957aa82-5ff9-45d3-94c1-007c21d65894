// 测试日期禁用逻辑
const dayjs = require('dayjs');

// 模拟 formData
const formData = {
  rangeDate: ['2025-07-11 11:59:36', '2025-07-11 11:59:36']
};

// 模拟 disabledSignUpRangeDate 函数
const disabledSignUpRangeDate = (current) => {
  // 如果活动时间未选择，只禁用今天之前的日期
  if (!formData.rangeDate[0] || !formData.rangeDate[1]) {
    const isDisabled = dayjs(current).isBefore(dayjs().startOf('day'));
    return isDisabled;
  }
  
  // 获取活动开始和结束日期（只比较日期，不比较时间）
  const activityStartDate = dayjs(formData.rangeDate[0]).startOf('day');
  const activityEndDate = dayjs(formData.rangeDate[1]).startOf('day');
  const currentDate = dayjs(current).startOf('day');
  
  // 报名时间应该在活动时间范围内（包含边界）
  // 禁用：小于活动开始日期 或 大于活动结束日期
  const isDisabled = currentDate.isBefore(activityStartDate) || currentDate.isAfter(activityEndDate);
  
  console.log('检查日期:', {
    current: currentDate.format('YYYY-MM-DD'),
    activityStart: activityStartDate.format('YYYY-MM-DD'),
    activityEnd: activityEndDate.format('YYYY-MM-DD'),
    isDisabled,
    rangeDate: formData.rangeDate
  });
  
  return isDisabled;
};

// 测试用例
console.log('=== 测试场景：活动时间 2025-07-11 11:59:36 - 2025-07-11 11:59:36 ===');
console.log('2025-07-10 是否禁用:', disabledSignUpRangeDate('2025-07-10'));
console.log('2025-07-11 是否禁用:', disabledSignUpRangeDate('2025-07-11'));
console.log('2025-07-12 是否禁用:', disabledSignUpRangeDate('2025-07-12'));

// 清空活动时间的情况
console.log('\n=== 测试场景：活动时间被清空 ===');
formData.rangeDate = ['', ''];
console.log('2025-07-11 是否禁用:', disabledSignUpRangeDate('2025-07-11'));
